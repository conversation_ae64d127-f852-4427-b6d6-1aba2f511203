<route lang="jsonc" type="page">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "老板抽卡"
  }
}
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import Turntable from './turntable.vue'

defineOptions({
  name: 'DrawCard',
})

// 转盘相关数据
const userId = ref('')
const shouldInitializeTurntable = ref(false)
const addSpinsCount = ref(0)
const spinResult = ref('')
const totalValue = ref(0)
const showTurntable = ref(false)

// 卡片类型定义
interface Card {
  id: number
  name: string
  rarity: 'legendary' | 'epic' | 'rare' | 'common'
  image: string
  description: string
}

// 卡片数据
const cards = ref<Card[]>([
  { id: 1, name: '传说级老板', rarity: 'legendary', image: '🎯', description: '超级稀有的老板卡' },
  { id: 2, name: '史诗级老板', rarity: 'epic', image: '⭐', description: '非常稀有的老板卡' },
  { id: 3, name: '稀有级老板', rarity: 'rare', image: '💎', description: '稀有的老板卡' },
  { id: 4, name: '普通级老板', rarity: 'common', image: '🔸', description: '常见的老板卡' },
])

// 抽卡结果
const drawResult = ref<Card | null>(null)
const isDrawing = ref(false)

// 抽卡概率
const rarityProbability = {
  legendary: 0.01, // 1%
  epic: 0.05, // 5%
  rare: 0.24, // 24%
  common: 0.70, // 70%
}

// 抽卡函数
async function drawCard() {
  if (isDrawing.value)
    return

  isDrawing.value = true
  drawResult.value = null

  try {
    // 这里可以调用实际的抽卡API
    // 模拟抽卡动画延迟
    await new Promise(resolve => setTimeout(resolve, 2000))

    const random = Math.random()
    let selectedCard: Card | undefined

    if (random < rarityProbability.legendary) {
      selectedCard = cards.value.find(card => card.rarity === 'legendary')
    }
    else if (random < rarityProbability.legendary + rarityProbability.epic) {
      selectedCard = cards.value.find(card => card.rarity === 'epic')
    }
    else if (random < rarityProbability.legendary + rarityProbability.epic + rarityProbability.rare) {
      selectedCard = cards.value.find(card => card.rarity === 'rare')
    }
    else {
      selectedCard = cards.value.find(card => card.rarity === 'common')
    }

    drawResult.value = selectedCard || null

    // 抽卡成功后显示转盘并给予转盘次数
    if (selectedCard) {
      showTurntable.value = true
      addSpinsCount.value = 1 // 每次抽卡成功给1次转盘机会
      shouldInitializeTurntable.value = true

      uni.showToast({
        title: `获得 1 次转盘机会！`,
        icon: 'success',
      })
    }
  }
  catch (error) {
    console.error('抽卡失败:', error)
    uni.showToast({
      title: '抽卡失败，请重试',
      icon: 'error',
    })
  }
  finally {
    isDrawing.value = false
  }
}

// 获取稀有度颜色
function getRarityColor(rarity: Card['rarity']) {
  const colors: Record<Card['rarity'], string> = {
    legendary: '#ff6b35',
    epic: '#9b59b6',
    rare: '#3498db',
    common: '#95a5a6',
  }
  return colors[rarity] || colors.common
}

// 重置抽卡
function resetDraw() {
  drawResult.value = null
}

// 转盘事件处理
function onTurntableReady() {
  console.log('转盘已准备就绪')
}

function onSpinResult(prize: any) {
  spinResult.value = `恭喜获得: ${prize.name}`
  if (prize.value) {
    totalValue.value += prize.value
  }

  uni.showToast({
    title: `恭喜获得: ${prize.name}`,
    icon: 'success',
  })
}

function onInitialSpinsValue(value: number) {
  totalValue.value += value
}

// 页面加载时获取用户ID
onMounted(() => {
  // 从URL参数获取用户ID
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}
  userId.value = options.id || 'test-user-123' // 提供默认测试用户ID

  if (userId.value) {
    // 如果有用户ID，可以在这里初始化一些数据
    console.log('用户ID:', userId.value)
  }
})
</script>

<template>
  <Turntable
    :user-id="userId"
    :should-initialize="shouldInitializeTurntable"
    :add-spins-count="addSpinsCount"
    @turntable-ready="onTurntableReady"
    @spin-result="onSpinResult"
    @initial-spins-value="onInitialSpinsValue"
  />
</template>

<style lang="scss" scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 1s ease-in-out;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
</style>
