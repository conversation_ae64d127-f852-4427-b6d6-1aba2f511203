# Turntable组件unibest最佳实践重构

## 重构概述

按照unibest框架的最佳实践，将turntable组件的接口调用进行了全面重构，使用了标准的API管理和请求处理方式。

## 主要改动

### 1. 创建API接口文件 (`src/api/turntable.ts`)

按照unibest规范，将所有接口调用集中管理：

```typescript
import { http } from '@/http/http'

// 接口类型定义
export interface IPrize {
  name: string
  value: number
}

export interface ISpinResult {
  name: string
  value: number
  timestamp: string
}

export interface IPrizesResponse {
  prizes: IPrize[]
  remainingSpins: number
  spinResults?: ISpinResult[]
}

export interface ISpinResponse {
  prize: IPrize
  prizeIndex: number
  remainingSpins: number
}

// API函数
export function getTurntablePrizesAPI(userId: string) {
  return http.get<IPrizesResponse>('/Mon/prizes', { id: userId })
}

export function spinTurntableAPI(userId: string) {
  return http.get<ISpinResponse>('/Mon/spin', { id: userId })
}
```

### 2. 使用useRequest管理请求状态

替换原来的fetch调用，使用unibest推荐的useRequest：

```typescript
// 使用useRequest管理获取奖品信息的请求状态
const {
  loading: prizesLoading,
  error: prizesError,
  data: prizesData,
  run: fetchPrizes
} = useRequest<IPrizesResponse>(() => getTurntablePrizesAPI(props.userId), {
  immediate: false
})

// 使用useRequest管理抽奖请求状态
const {
  loading: spinLoading,
  error: spinError,
  data: spinData,
  run: executeSpin
} = useRequest<ISpinResponse>(() => spinTurntableAPI(props.userId), {
  immediate: false
})
```

### 3. 重构初始化函数

```typescript
async function initializeTurntable() {
  if (isInitialized.value) return
  isInitialized.value = true

  try {
    // 如果没有userId，使用模拟数据
    if (!props.userId) {
      // 模拟数据逻辑...
      return
    }

    // 使用useRequest获取奖品数据
    const data = await fetchPrizes()
    if (data) {
      wheelPrizes.value = data.prizes
      spinCount.value = data.remainingSpins
      // 处理历史记录...
    }
  } catch (error) {
    // 错误处理和降级逻辑...
  }
}
```

### 4. 重构抽奖函数

```typescript
async function handleSpin() {
  if (clickLock.value || spinCount.value <= 0 || buttonDisabled.value) {
    return
  }
  clickLock.value = true
  buttonDisabled.value = true

  try {
    let prize: IPrize
    let prizeIndex: number

    if (!props.userId) {
      // 模拟逻辑...
    } else {
      // 使用useRequest执行抽奖
      const data = await executeSpin()
      if (data) {
        prize = data.prize
        prizeIndex = data.prizeIndex
        spinCount.value = data.remainingSpins
      } else {
        throw new Error('抽奖失败')
      }
    }

    // 执行转盘动画和更新UI...
  } catch (error) {
    // 错误处理和降级逻辑...
  }
}
```

### 5. 环境变量配置

修改了`.env`文件，将后端地址设置为指定的地址：

```env
# 第一个请求地址
VITE_SERVER_BASEURL = 'http://localhost:8888'
# 第二个请求地址
VITE_API_SECONDARY_URL = 'http://localhost:8888'

VITE_UPLOAD_BASEURL = 'http://localhost:8888/upload'
```

## 技术优势

### 1. **标准化API管理**
- 所有接口集中在`src/api/turntable.ts`
- 统一的类型定义和错误处理
- 便于维护和测试

### 2. **请求状态管理**
- 使用`useRequest`自动管理loading、error、data状态
- 内置错误处理和重试机制
- 更好的用户体验

### 3. **类型安全**
- 完整的TypeScript类型定义
- 编译时类型检查
- 更好的IDE支持

### 4. **错误处理**
- 统一的错误处理机制
- 优雅的降级策略
- 用户友好的错误提示

### 5. **可维护性**
- 符合unibest最佳实践
- 清晰的代码结构
- 易于扩展和修改

## 接口规范

### 获取奖品信息
```
GET http://localhost:8888/Mon/prizes?id={userId}

Response:
{
  "data": {
    "prizes": [
      {"name": "现金红包", "value": 10},
      {"name": "优惠券", "value": 5},
      // ... 6个奖品
    ],
    "remainingSpins": 3,
    "spinResults": [
      {"name": "现金红包", "value": 10, "timestamp": "2023-01-01T00:00:00Z"}
    ]
  }
}
```

### 执行抽奖
```
GET http://localhost:8888/Mon/spin?id={userId}

Response:
{
  "data": {
    "prize": {"name": "现金红包", "value": 10},
    "prizeIndex": 0,
    "remainingSpins": 2
  }
}
```

## 使用方法

组件使用方式保持不变：

```vue
<template>
  <Turntable
    :user-id="userId"
    :should-initialize="shouldInitialize"
    :add-spins-count="addSpinsCount"
    @turntable-ready="onTurntableReady"
    @spin-result="onSpinResult"
    @initial-spins-value="onInitialSpinsValue"
  />
</template>
```

## 测试和验证

✅ 项目编译成功  
✅ 接口调用符合unibest规范  
✅ 错误处理和降级机制正常  
✅ 类型安全检查通过  
✅ 支持模拟模式测试  

## 总结

通过这次重构，turntable组件完全符合了unibest框架的最佳实践：

1. **API管理标准化** - 使用统一的API文件管理接口
2. **请求处理规范化** - 使用useRequest处理请求状态
3. **类型安全** - 完整的TypeScript类型定义
4. **错误处理** - 统一的错误处理和降级机制
5. **可维护性** - 清晰的代码结构和文档

这样的重构不仅提高了代码质量，也为后续的功能扩展和维护奠定了良好的基础。
